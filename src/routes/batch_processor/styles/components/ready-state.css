/**
 * ═══════════════════════════════════════════════════════════════════════════════
 * 🎯 READY STATE STYLES - 准备就绪状态样式
 * ═══════════════════════════════════════════════════════════════════════════════
 *
 * 专门为"准备就绪"状态设计的全屏样式
 * 无阴影、占据整个屏幕高度、排版合理
 *
 * @version 1.0
 * <AUTHOR> Agent
 * @updated 2025-07-28
 */

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎨 READY STATE CONTAINER - 准备就绪容器
 * ═══════════════════════════════════════════════════════════════════════════ */

.ready-state-container {
  /* 移除所有阴影效果 */
  box-shadow: none !important;

  /* 背景渐变 - 淡雅的蓝色渐变 */
  background: linear-gradient(135deg,
      rgba(248, 250, 252, 0.8) 0%,
      rgba(241, 245, 249, 0.9) 25%,
      rgba(226, 232, 240, 0.8) 50%,
      rgba(241, 245, 249, 0.9) 75%,
      rgba(248, 250, 252, 0.8) 100%);

  /* 边框和圆角 */
  border: none;
  border-radius: 0;

  /* 动画效果 */
  animation: readyStateFloat 6s ease-in-out infinite;

  /* 确保占据全部空间 */
  width: 100%;
  height: 100%;
  min-height: 100%;

  /* 调整对齐方式 - 稍微偏上 */
  align-items: flex-start;
  padding-top: 15vh;
  /* 从顶部15%开始 */

  /* 定位和层级 */
  position: relative;
  z-index: 1;
}

.ready-state-content {
  /* 移除所有阴影 */
  box-shadow: none !important;

  /* 背景透明 */
  background: transparent;

  /* 动画效果 */
  animation: readyContentPulse 4s ease-in-out infinite;

  /* 确保内容居中 */
  position: relative;
  z-index: 2;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * ✨ ANIMATIONS - 动画效果
 * ═══════════════════════════════════════════════════════════════════════════ */

@keyframes readyStateFloat {

  0%,
  100% {
    background: linear-gradient(135deg,
        rgba(248, 250, 252, 0.8) 0%,
        rgba(241, 245, 249, 0.9) 25%,
        rgba(226, 232, 240, 0.8) 50%,
        rgba(241, 245, 249, 0.9) 75%,
        rgba(248, 250, 252, 0.8) 100%);
  }

  50% {
    background: linear-gradient(135deg,
        rgba(241, 245, 249, 0.9) 0%,
        rgba(226, 232, 240, 0.8) 25%,
        rgba(219, 234, 254, 0.7) 50%,
        rgba(226, 232, 240, 0.8) 75%,
        rgba(241, 245, 249, 0.9) 100%);
  }
}

@keyframes readyContentPulse {

  0%,
  100% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }

  50% {
    transform: translateY(-2px) scale(1.005);
    opacity: 0.98;
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎯 ICON ENHANCEMENTS - 图标增强
 * ═══════════════════════════════════════════════════════════════════════════ */

.ready-state-content .ready-icon-animated {
  /* 增强动画效果 */
  animation: readyIconFloat 3s ease-in-out infinite;

  /* 移除阴影 */
  box-shadow: none !important;

  /* 增加尺寸 */
  transform: scale(1.2);
}

@keyframes readyIconFloat {

  0%,
  100% {
    transform: scale(1.2) translateY(0) rotate(0deg);
  }

  33% {
    transform: scale(1.25) translateY(-3px) rotate(1deg);
  }

  66% {
    transform: scale(1.15) translateY(-1px) rotate(-1deg);
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📱 RESPONSIVE DESIGN - 响应式设计
 * ═══════════════════════════════════════════════════════════════════════════ */

@media (max-width: 768px) {
  .ready-state-container {
    padding-top: 10vh;
    /* 移动端减少顶部间距 */
  }

  .ready-state-content {
    padding: 2rem 1rem;
    max-width: 100%;
  }

  .ready-state-content .ready-icon-animated {
    transform: scale(1);
  }

  .ready-state-content h2 {
    font-size: 2rem !important;
  }

  .ready-state-content p {
    font-size: 1rem !important;
  }
}

@media (max-width: 480px) {
  .ready-state-container {
    padding-top: 8vh;
    /* 小屏幕进一步减少顶部间距 */
  }

  .ready-state-content {
    padding: 1.5rem 0.75rem;
  }

  .ready-state-content h2 {
    font-size: 1.75rem !important;
    margin-bottom: 1rem !important;
  }

  .ready-state-content p {
    font-size: 0.875rem !important;
    margin-bottom: 1.5rem !important;
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎨 HOVER EFFECTS - 悬停效果
 * ═══════════════════════════════════════════════════════════════════════════ */

.ready-state-container:hover {
  /* 悬停时稍微增强背景 */
  background: linear-gradient(135deg,
      rgba(241, 245, 249, 0.9) 0%,
      rgba(226, 232, 240, 0.95) 25%,
      rgba(219, 234, 254, 0.8) 50%,
      rgba(226, 232, 240, 0.95) 75%,
      rgba(241, 245, 249, 0.9) 100%);

  /* 保持无阴影 */
  box-shadow: none !important;
}

.ready-state-content:hover .ready-icon-animated {
  animation-duration: 2s;
  transform: scale(1.3);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🚫 SHADOW REMOVAL - 强制移除阴影
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 确保准备就绪状态下的所有元素都没有阴影 */
.ready-state-container,
.ready-state-container *,
.ready-state-content,
.ready-state-content * {
  box-shadow: none !important;
}

/* 特别针对可能继承阴影的元素 */
.ready-state-container .glass-card,
.ready-state-container .card,
.ready-state-container .enhanced-card {
  box-shadow: none !important;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎯 ACCESSIBILITY - 无障碍优化
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {

  .ready-state-container,
  .ready-state-content,
  .ready-state-content .ready-icon-animated {
    animation: none;
  }

  .ready-state-container:hover,
  .ready-state-content:hover .ready-icon-animated {
    transform: none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .ready-state-container {
    background: #f8fafc;
    border: 2px solid #1e293b;
  }

  .ready-state-content h2 {
    color: #0f172a !important;
  }

  .ready-state-content p {
    color: #334155 !important;
  }
}